/*
 * CSS Común para todos los formularios en la carpeta forms/
 * Archivo creado para evitar duplicación de código CSS
 */

:root {
    --primary-color: #0078d4; /* Microsoft Blue */
    --border-color: #c8c6c4;
    --input-bg-color: #fff;
    --input-focus-border: #005a9e;
    --text-color: #323130;
    --label-color: #323130;
    --placeholder-color: #605e5c;
    --error-color: #a80000;
    --body-bg-color: #f3f2f1; /* Light gray background */
    --form-bg-color: #fff;
    --section-border-color: #eaeaea;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: var(--body-bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

.form-container {
    max-width: 800px;
    margin: 20px auto;
    background-color: var(--form-bg-color);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

h1 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 25px;
    font-size: 2em;
    font-weight: 600;
}

.form-subtitle {
    text-align: center;
    color: var(--placeholder-color);
    font-size: 0.9em;
    margin-bottom: 25px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.form-note {
    font-size: 0.9em;
    color: var(--placeholder-color);
    margin-bottom: 15px;
    padding-left: 5px;
}

.form-note.header-note {
    text-align: center;
    margin-top: -15px;
}

fieldset {
    border: none; /* Remove default fieldset border */
    padding: 0;
    margin-bottom: 30px;
}

legend {
    font-weight: 600;
    font-size: 1.4em;
    color: var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
    width: 100%;
    border-bottom: 1px solid var(--section-border-color);
}

.form-group {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid var(--section-border-color);
    border-radius: 4px;
    background-color: #fdfdfd; /* Slightly off-white for question groups */
}

label, .radio-group-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 1em;
    color: var(--label-color);
}

input[type="text"],
input[type="date"],
input[type="email"],
input[type="number"],
textarea,
select {
    width: 100%;
    padding: 10px 12px;
    margin-bottom: 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 1em;
    background-color: var(--input-bg-color);
    color: var(--text-color);
}

input[type="text"]::placeholder,
textarea::placeholder {
    color: var(--placeholder-color);
}

input[type="text"]:focus,
input[type="date"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3);
}

textarea {
    min-height: 80px;
    resize: vertical;
}

.radio-group div, .checkbox-group div {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.radio-group input[type="radio"], .checkbox-group input[type="checkbox"] {
    margin-right: 10px;
    width: auto; /* Override full width for radios/checkboxes */
    accent-color: var(--primary-color); /* Modern way to color radio/checkbox */
}

.radio-group label, .checkbox-group label {
    font-weight: normal;
    margin-bottom: 0; /* Reset margin for inline labels */
}

.asterisk {
    color: var(--error-color);
    margin-left: 2px;
}

button[type="submit"] {
    display: block;
    width: auto;
    min-width: 150px;
    padding: 12px 25px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: 600;
    transition: background-color 0.2s ease-in-out;
    margin: 30px auto 0; /* Center button */
}

button[type="submit"]:hover {
    background-color: var(--input-focus-border);
}

.footer-text {
    font-size:0.8em;
    color: var(--placeholder-color);
    text-align: center;
    margin-top:40px;
}

.microsoft-forms-logo { /* Placeholder for potential logo */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    font-size: 0.9em;
    color: var(--placeholder-color);
}

.microsoft-forms-logo img {
    height: 16px;
    margin-right: 8px;
}

/* Estilos específicos para el formulario spotcheck */
.spotcheck h1 {
    margin-bottom: 10px; /* Reduced margin for subtitle in spotcheck */
}

/* Estilos para el componente de búsqueda en selects */
.searchable-select-container {
    position: relative;
    width: 100%;
}

.searchable-select-container .search-input {
    width: 100%;
    padding: 10px 12px;
    margin-bottom: 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 1em;
    background-color: var(--input-bg-color);
    color: var(--text-color);
}

/* Cuando las opciones están visibles, ajustar el borde del input */
.searchable-select-container .search-input.options-visible {
    border-radius: 4px 4px 0 0;
    border-bottom: none;
}

.searchable-select-container .search-input:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3);
}

.searchable-select-container .search-input::placeholder {
    color: var(--placeholder-color);
}

.searchable-select-container .options-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 4px 4px;
    background-color: var(--input-bg-color);
    position: absolute;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.searchable-select-container .option-item {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.searchable-select-container .option-item:hover {
    background-color: #f5f5f5;
}

.searchable-select-container .option-item.selected {
    background-color: var(--primary-color);
    color: white;
}

.searchable-select-container .option-item.highlighted {
    background-color: #e3f2fd;
    border-left: 3px solid var(--primary-color);
}

.searchable-select-container .option-item.hidden {
    display: none;
}

.searchable-select-container .no-results {
    padding: 10px 12px;
    color: var(--placeholder-color);
    font-style: italic;
    text-align: center;
}

/* Ocultar el select original cuando se convierte a searchable */
select.original-select {
    display: none !important;
}
